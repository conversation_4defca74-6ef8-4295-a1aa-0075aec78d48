<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.buguagaoshu.tiktube.dao.OSSConfigDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.buguagaoshu.tiktube.entity.OSSConfigEntity" id="ossConfigMap">
        <result property="id" column="id"/>
        <result property="configName" column="config_name"/>
        <result property="bucketName" column="bucket_name"/>
        <result property="endpoint" column="endpoint"/>
        <result property="accessKey" column="access_key"/>
        <result property="secretKey" column="secret_key"/>
        <result property="region" column="region"/>
        <result property="urlPrefix" column="url_prefix"/>
        <result property="pathStyleAccess" column="path_style_access"/>
        <result property="other" column="other"/>
        <result property="createTime" column="create_time"/>
        <result property="status" column="status"/>
        <result property="creatorId" column="creator_id"/>
        <result property="updaterId" column="updater_id"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

</mapper>