<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.buguagaoshu.tiktube.dao.AiConfigDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.buguagaoshu.tiktube.entity.AiConfigEntity" id="aiConfigMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="apiUrl" column="api_url"/>
        <result property="apiKey" column="api_key"/>
        <result property="model" column="model"/>
        <result property="prompt" column="prompt"/>
        <result property="type" column="type"/>
        <result property="createTime" column="create_time"/>
        <result property="createUser" column="create_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="status" column="status"/>
        <result property="useTokens" column="use_tokens"/>
        <result property="maxTokens" column="max_tokens"/>
    </resultMap>

    <update id="batchUpdateCount">
        UPDATE ai_config
        SET ${col} = CASE id
        <foreach collection="countMap.entrySet()" item="count" index="id">
            WHEN #{id} THEN ${col} + #{count}
        </foreach>
        END
        WHERE id IN
        <foreach collection="countMap.keySet()" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>


</mapper>