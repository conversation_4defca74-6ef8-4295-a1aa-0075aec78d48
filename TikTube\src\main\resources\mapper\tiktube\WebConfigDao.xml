<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.buguagaoshu.tiktube.dao.WebConfigDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.buguagaoshu.tiktube.entity.WebConfigEntity" id="webConfigMap">
        <result property="id" column="id"/>
        <result property="jsonText" column="json_text"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="ip" column="ip"/>
    </resultMap>
    
    <select id="findNewConfig" resultType="com.buguagaoshu.tiktube.entity.WebConfigEntity">
        select * from web_config order by id desc limit 1
    </select>

</mapper>