<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.buguagaoshu.tiktube.dao.NotificationDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.buguagaoshu.tiktube.entity.NotificationEntity" id="notificationMap">
        <result property="id" column="id"/>
        <result property="notifier" column="notifier"/>
        <result property="receiver" column="receiver"/>
        <result property="outerId" column="outer_id"/>
        <result property="articleId" column="article_id"/>
        <result property="title" column="title"/>
        <result property="linkMessage" column="link_message" />
        <result property="content" column="content"/>
        <result property="commentId" column="comment_id"/>
        <result property="type" column="type"/>
        <result property="createTime" column="create_time"/>
        <result property="readTime" column="read_time"/>
        <result property="status" column="status"/>
    </resultMap>

    <update id="updateAllNotificationStatus">
        UPDATE notification
        SET status = #{status}, read_time = #{readTime}
        WHERE receiver = #{userId} AND status = 0
    </update>

</mapper>