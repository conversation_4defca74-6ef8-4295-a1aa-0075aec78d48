<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.buguagaoshu.tiktube.dao.CommentDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.buguagaoshu.tiktube.entity.CommentEntity" id="commentMap">
        <result property="id" column="id"/>
        <result property="articleId" column="article_id"/>
        <result property="userId" column="user_id"/>
        <result property="comment" column="comment"/>
        <result property="parentCommentId" column="parent_comment_id"/>
        <result property="parentUserId" column="parent_user_id"/>
        <result property="likeCount" column="like_count"/>
        <result property="commentCount" column="comment_count"/>
        <result property="dislikeCount" column="dislike_count"/>
        <result property="type" column="type"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="ua" column="ua"/>
        <result property="ip" column="ip"/>
        <result property="city" column="city"/>
        <result property="commentId" column="comment_id"/>
        <result property="aiExamineMessage" column="ai_examine_message"/>
        <result property="aiExamineToken" column="ai_examine_token"/>
        <result property="aiExamineId" column="ai_examine_id"/>
    </resultMap>
    
    <update id="addCount">
        update comment set ${col} = ${col} + #{count} where id = #{id}
    </update>
    
    <!-- 批量更新评论计数的SQL - 优化版 -->
    <update id="batchUpdateCount">
        UPDATE comment
        SET ${col} = CASE id
        <foreach collection="countMap.entrySet()" item="count" index="id">
            WHEN #{id} THEN ${col} + #{count}
        </foreach>
        END
        WHERE id IN
        <foreach collection="countMap.keySet()" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

</mapper>