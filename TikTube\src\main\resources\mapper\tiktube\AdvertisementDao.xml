<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.buguagaoshu.tiktube.dao.AdvertisementDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.buguagaoshu.tiktube.entity.AdvertisementEntity" id="advertisementMap">
        <result property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="url" column="url"/>
        <result property="imageUrl" column="image_url"/>
        <result property="content" column="content"/>
        <result property="videoUrl" column="video_url"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createUser" column="create_user"/>
        <result property="status" column="status"/>
        <result property="type" column="type"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="viewCount" column="view_count"/>
        <result property="position" column="position"/>
    </resultMap>
    
    <!-- 批量更新广告浏览量 -->
    <update id="batchUpdateCount">
        UPDATE advertisement_table
        SET ${col} = CASE id
        <foreach collection="countMap.entrySet()" item="count" index="id">
            WHEN #{id} THEN ${col} + #{count}
        </foreach>
        END
        WHERE id IN
        <foreach collection="countMap.keySet()" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

</mapper>