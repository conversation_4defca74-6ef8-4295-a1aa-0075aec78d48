<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.buguagaoshu.tiktube.dao.UserRoleDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.buguagaoshu.tiktube.entity.UserRoleEntity" id="userRoleMap">
        <result property="id" column="id"/>
        <result property="userid" column="userId"/>
        <result property="role" column="role"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="vipStartTime" column="vip_start_time"/>
        <result property="vipStopTime" column="vip_stop_time"/>
        <result property="modified" column="modified"/>
    </resultMap>


</mapper>