<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.buguagaoshu.tiktube.dao.ArticleDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.buguagaoshu.tiktube.entity.ArticleEntity" id="articleMap">
        <result property="id" column="id"/>
        <result property="imgUrl" column="img_url"/>
        <result property="title" column="title"/>
        <result property="describes" column="describes"/>
        <result property="viewCount" column="view_count"/>
        <result property="likeCount" column="like_count"/>
        <result property="favoriteCount" column="favorite_count"/>
        <result property="dislikeCount" column="dislike_count"/>
        <result property="examineStatus" column="examine_status"/>
        <result property="examineMessage" column="examine_message"/>
        <result property="category" column="category"/>
        <result property="tag" column="tag"/>
        <result property="userId" column="user_id"/>
        <result property="type" column="type"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="status" column="status"/>
        <result property="score" column="score"/>
        <result property="scoreCount" column="score_count"/>
        <result property="commentCount" column="comment_count"/>
        <result property="danmakuCount" column="danmaku_count"/>
        <result property="examineUser" column="examine_user"/>
        <result property="duration" column="duration"/>
        <result property="pixelsNumber" column="pixels_number"/>
        <result property="frameRate" column="frame_rate"/>
        <result property="ua" column="ua"/>
        <result property="ip" column="ip"/>
        <result property="city" column="city"/>
    </resultMap>
    
    <update id="addViewCount">
        update article set view_count = view_count + #{count} where id = #{id}
    </update>
    
    <update id="addCount">
        update article set ${col} = ${col} + #{count} where id = #{id}
    </update>
    
    <update id="addDanmakuCount">
        update article set danmaku_count = danmaku_count + #{count} where id = #{id}
    </update>
    
    <!-- 批量更新文章计数的SQL - 优化版 -->
    <update id="batchUpdateCount">
        UPDATE article
        SET ${col} = CASE id
        <foreach collection="countMap.entrySet()" item="count" index="id">
            WHEN #{id} THEN ${col} + #{count}
        </foreach>
        END
        WHERE id IN
        <foreach collection="countMap.keySet()" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="findHotArticlesWithScore" resultMap="articleMap">
        SELECT *,
               (
                   view_count +
                   comment_count * 2 +
                   favorite_count * 4 +
                   danmaku_count * 1.5 +
                   like_count * 2 -
                   dislike_count * 2
               ) AS hot_score
        FROM article
        WHERE status = 0
          AND examine_status = 1 AND type = #{type}
          <if test="startTime > 0">
              AND update_time >= #{startTime}
          </if>
        ORDER BY hot_score DESC
        <if test="limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <select id="findLatestArticlesWithScore" resultMap="articleMap">
        SELECT *,
               (
                   view_count +
                   comment_count * 2 +
                   favorite_count * 4 +
                   danmaku_count * 1.5 +
                   like_count * 2 -
                   dislike_count * 2
               ) AS hot_score
        FROM article
        WHERE status = 0
          AND examine_status = 1
        ORDER BY update_time DESC
        LIMIT #{limit}
    </select>

    <!-- 根据标签查找相似文章 -->
    <select id="findSimilarArticlesByTags" resultMap="articleMap">
        SELECT *
        FROM article
        WHERE status = 0
          AND examine_status = 1
          <if test="articleId != null">
              AND id != #{articleId}
          </if>
          AND (
            <foreach collection="tagLikeList" item="tagPattern" separator=" OR ">
                tag LIKE #{tagPattern}
            </foreach>
          )
        ORDER BY update_time DESC
        <if test="limit > 0">
            LIMIT #{limit}
        </if>
    </select>

</mapper>