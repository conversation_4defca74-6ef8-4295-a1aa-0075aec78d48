<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.buguagaoshu.tiktube.dao.ArticleTextDao">

    <resultMap id="articleTextMap" type="com.buguagaoshu.tiktube.entity.ArticleTextEntity">
        <result property="id" column="id"/>
        <result property="articleId" column="article_id"/>
        <result property="content" column="content"/>
        <result property="userId" column="user_id"/>
        <result property="type" column="type"/>
        <result property="password" column="password"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="sort" column="sort"/>
        <result property="status" column="status"/>
    </resultMap>
</mapper>