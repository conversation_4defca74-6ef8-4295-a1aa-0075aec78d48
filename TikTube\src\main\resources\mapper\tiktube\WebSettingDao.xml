<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.buguagaoshu.tiktube.dao.WebSettingDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.buguagaoshu.tiktube.entity.WebSettingEntity" id="webSettingMap">
        <result property="name" column="name"/>
        <result property="openNoVipLimit" column="open_no_vip_limit"/>
        <result property="noVipViewCount" column="no_vip_view_count"/>
        <result property="logoUrl" column="logo_url"/>
        <result property="openInvitationRegister" column="open_invitation_register"/>
        <result property="webDescribe" column="web_describe"/>
        <result property="openUploadVideoAddViewCount" column="open_upload_video_add_view_count"/>
        <result property="openExamine" column="open_examine"/>
        <result property="id" column="id"/>
        <result property="createTime" column="create_time"/>
        <result property="homeMaxVideoCount" column="home_max_video_count"/>
    </resultMap>
    <select id="findNewSetting" resultType="com.buguagaoshu.tiktube.entity.WebSettingEntity">
        select * from web_setting order by id desc limit 1
    </select>


</mapper>