<div align="center">
    <img src="/img/logo.png" alt="logo" title="logo" width="50%" style="text-align:center;">
</div>

<br/>

<div align="center">
    <a href="/README_zh_CN.md">简体中文</a>
</div>

<br/>

# TikTube A simple video website with a bullet chat (danmaku) feature


## introduction 

A Simple Video Website

The website name is a mashup of TikTok and YouTube, with the logo generated by Doubao AI.

The main interface is inspired by [YouTube](https://www.youtube.com/), with some features borrowed from [Bilibili](https://www.bilibili.com/).  

Backend: Spring Boot, MySQL

Frontend: Vue, Vuetifyjs  

All core features have been completed, including:  

- Video submission  
- Danmaku (bullet comments)  
- Video playback  
- Watch history, favorites, comments, likes  
- Automatic cover image generation  
- Data management  
- Login with TOTP two-factor authentication  
- Support for multiple storage backends, compatible with S3 API-based object storage (tested with MinIO and Cloudflare R2)  
- Reporting and content moderation  
- AI Content Review
- Announcements, notifications, etc.

Some minor features are still being optimized.  


For ease of use and deployment, the only external dependency is the database, with Redis as an optional configuration.  

Enable Redis caching by setting the `open-redis` option to `true` in the `application.yml` file.  


Regarding the Online DEMO: [https://tiktube.buguagaoshu.com/](https://tiktube.buguagaoshu.com/)

*This version of the DEMO is for display purposes only. As such, the administrator has disabled submission and commenting functions for regular users. If you wish to experience these features, please deploy the system yourself for testing.*

Test account:

Email: <EMAIL>

Password: test123456test


TOTP TEST Test account:

Email: <EMAIL>

Password: test123456test

TOTP secret：JL2ZZV7W6OGXG4JYLCVXZRMDUV4XA3DDLPC3Q72IO6XB4K4EJKW4VW4IHXOMA2DCYU6WWRMNAMKXCVO7PGUIK2PCJG7TXMDSTA2JP76XV3BGAGSJAW66LFFQQYOG2KYB

you can use ：如Google Authenticator、Microsoft Authenticator


<img src="/img/otp.png" title="otp" alt="otp">


## Video demo

YouTube：<a href="https://youtu.be/HX3812cRtYA" target="_blank">https://youtu.be/HX3812cRtYA</a>

Bilibili：<a href="https://www.bilibili.com/video/BV1AV59z5ESV" target="_blank">https://www.bilibili.com/video/BV1AV59z5ESV</a>

## Quick start

**Run Environment: Java17+, Node 20+, Maven 3.9+，MySQL 8.0+**

Creating a database, Configuring Database Addresses

If you have a Redis service, you can enable Redis caching by setting the `open-redis` option to `true` in `application.yml`. In this case, the system will use Redis for caching.  

This option defaults to `false`, meaning the system will use in-memory caching instead.  


**Run back end server**

```bash
cd TikTube
mvn clean package
```

**Then**

```bash
java -jar target/tiktube-
```

**Run front end server**

```bash
cd TikTubeWeb
npm install
```

**Then**

```bash
npm run dev
```

**Final**

Open

```
http://127.0.0.1:5173
```


## screenshot

### Home

<img src="/img/home.png" title="Home" alt="Home">

### Video Page

<img src="/img/video.png" title="Video Page" alt="Video Page">

### Comment

<img src="/img/comment.png" title="Comment" alt="Comment">

### History

<img src="/img/history.png" title="History" alt="History">

### Subscribe

<img src="/img/subscribe.png" title="Subscribe" alt="Subscribe">

### User Home

<img src="/img/user.png" title="User Home" alt="User Home">

### Notification

<img src="/img/notification.png" title="Notification" alt="Notification">

### Publish

<img src="/img/publish.png" title="Publish" alt="Publish">

#### Video Capture

<img src="/img/Capture.png" title="Video Capture" alt="Video Capture">


### Admin

<img src="/img/admin.png" title="Admin" alt="Admin">

### AI Examine

<img src="/img/ai.png" title="AI Examine" alt="AI Examine">


### Question

If you are unable to compile using an older version of the frontend

Frontend Fails to Compile

If you encounter the error `ESLint is not a constructor`, add `lintOnSave: false` to your `vue.config.js`.


**Tips:** The first user registered with the name admin will automatically become an administrator.

## CHANGE LOG

[CHANGELOG](/CHANGELOG.md)


## Other Links

GitHub：https://github.com/PuZhiweizuishuai/TikTube

Gitee: https://gitee.com/puzhiweizuishuai/VideoWeb
