package com.buguagaoshu.tiktube.utils;

/**
 * <AUTHOR> {@literal <EMAIL>}
 * create          2020-09-05 15:13
 */
public class PasswordUtil {
    public static String encode(String password) {
        BCryptPasswordEncoder bCryptPasswordEncoder = new BCryptPasswordEncoder();
        return bCryptPasswordEncoder.encode(password);
    }

    public static boolean judgePassword(String rawPassword, String encodedPassword) {
        BCryptPasswordEncoder bCryptPasswordEncoder = new BCryptPasswordEncoder();
        return bCryptPasswordEncoder.matches(rawPassword, encodedPassword);
    }
}
